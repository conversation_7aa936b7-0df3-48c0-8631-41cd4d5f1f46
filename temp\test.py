#!/usr/bin/env python3
"""
完整的VTK URDF查看器 - 基于urdf_integrated_viewer.py
包含关节控制、材质设置、运动学计算等完整功能
"""

import sys
import os
import numpy as np
import trimesh
import vtk
from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
from urdf_parser_py.urdf import URDF
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QLabel, QSlider, QFrame,
                             QGroupBox, QScrollArea, QSplitter, QComboBox,
                             QColorDialog, QSpinBox, QDoubleSpinBox)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QColor
import xml.etree.ElementTree as ET
from typing import Dict, List, Tuple, Optional


class URDFJointAnalyzer:
    """URDF关节分析器，提取可控关节信息"""

    def __init__(self, urdf_path: str):
        self.urdf_path = urdf_path
        self.joints_info = {}
        self._analyze_joints()

    def _analyze_joints(self):
        """分析URDF文件中的关节信息"""
        try:
            tree = ET.parse(self.urdf_path)
            root = tree.getroot()

            for joint in root.iter('joint'):
                joint_name = joint.get('name')
                joint_type = joint.get('type')

                if joint_type == 'fixed':
                    continue

                limit_elem = joint.find('limit')
                if limit_elem is not None:
                    lower = float(limit_elem.get('lower', '0'))
                    upper = float(limit_elem.get('upper', '0'))
                    effort = float(limit_elem.get('effort', '0'))
                    velocity = float(limit_elem.get('velocity', '1'))

                    if effort > 0:
                        self.joints_info[joint_name] = {
                            'type': joint_type,
                            'lower': lower,
                            'upper': upper,
                            'effort': effort,
                            'velocity': velocity
                        }

        except Exception as e:
            print(f"分析关节信息时出错: {e}")

    def get_controllable_joints(self) -> Dict:
        return self.joints_info

    def print_joint_info(self):
        print("\n=== 可控关节信息 ===")
        for name, info in self.joints_info.items():
            print(f"关节: {name}")
            print(f"  类型: {info['type']}")
            print(f"  范围: [{info['lower']:.3f}, {info['upper']:.3f}]")
            print(f"  最大力矩: {info['effort']}")
            print(f"  最大速度: {info['velocity']}")
            print()


class FingerGroupAnalyzer:
    """手指关节分组分析器"""

    def __init__(self, joint_analyzer: URDFJointAnalyzer):
        self.hand_type = "left" if "left" in joint_analyzer.urdf_path else "right"
        self.joint_analyzer = joint_analyzer
        self.finger_groups = self._analyze_finger_groups()

    def _analyze_finger_groups(self) -> Dict[str, Dict]:
        """分析并创建手指关节分组"""
        joint_info = self.joint_analyzer.get_controllable_joints()

        finger_patterns = {
            'thumb_rotation': {
                'name': '拇指转动',
                'joints': [f'{self.hand_type}_thumb_metacarpal_joint'],
                'ratios': [1.0],
                'description': '控制拇指左右转动'
            },
            'thumb_bend': {
                'name': '拇指弯曲',
                'joints': [f'{self.hand_type}_thumb_proximal_joint',
                           f'{self.hand_type}_thumb_distal_joint'],
                'ratios': [1.0, 0.8],
                'description': '控制拇指弯曲捏合'
            },
            'index': {
                'name': '食指',
                'joints': [f'{self.hand_type}_index_proximal_joint',
                           f'{self.hand_type}_index_distal_joint'],
                'ratios': [1.0, 0.8],
                'description': '控制食指弯曲'
            },
            'middle': {
                'name': '中指',
                'joints': [f'{self.hand_type}_middle_proximal_joint',
                           f'{self.hand_type}_middle_distal_joint'],
                'ratios': [1.0, 0.8],
                'description': '控制中指弯曲'
            },
            'ring': {
                'name': '无名指',
                'joints': [f'{self.hand_type}_ring_proximal_joint',
                           f'{self.hand_type}_ring_distal_joint'],
                'ratios': [1.0, 0.8],
                'description': '控制无名指弯曲'
            },
            'pinky': {
                'name': '小指',
                'joints': [f'{self.hand_type}_pinky_proximal_joint',
                           f'{self.hand_type}_pinky_distal_joint'],
                'ratios': [1.0, 0.8],
                'description': '控制小指弯曲'
            }
        }

        valid_groups = {}
        individual_joints = {}

        for group_key, group_config in finger_patterns.items():
            existing_joints = []
            existing_ratios = []

            for i, joint_name in enumerate(group_config['joints']):
                if joint_name in joint_info:
                    existing_joints.append(joint_name)
                    existing_ratios.append(group_config['ratios'][i])

            if len(existing_joints) >= 1:
                valid_groups[group_key] = {
                    'name': group_config['name'],
                    'joints': existing_joints,
                    'ratios': existing_ratios,
                    'joint_info': {joint: joint_info[joint] for joint in existing_joints},
                    'description': group_config.get('description', '')
                }

        # 收集所有已分组的关节
        all_finger_joints = set()
        for group in valid_groups.values():
            all_finger_joints.update(group['joints'])

        # 将不属于任何手指分组的关节归类为独立关节
        for joint_name, info in joint_info.items():
            if joint_name not in all_finger_joints:
                individual_joints[joint_name] = {
                    'name': joint_name,
                    'info': info
                }

        return {
            'finger_groups': valid_groups,
            'individual_joints': individual_joints
        }

    def get_finger_groups(self):
        return self.finger_groups


class SimpleTransformCalculator:
    """简化的变换计算器"""

    def __init__(self, robot):
        self.robot = robot
        self.joint_values = {}
        self.link_transforms = {}

        # 初始化所有非固定关节的值为0
        for joint in self.robot.joints:
            if joint.type != 'fixed':
                self.joint_values[joint.name] = 0.0

        self.calculate_all_transforms()

    def update_joint_value(self, joint_name: str, value: float):
        """更新关节值并重新计算变换"""
        if joint_name in self.joint_values:
            self.joint_values[joint_name] = value
            self.calculate_all_transforms()
            return self.get_affected_links(joint_name)
        return []

    def get_affected_links(self, joint_name: str):
        """获取受关节影响的链接"""
        affected = []
        joint = next((j for j in self.robot.joints if j.name == joint_name), None)
        if joint:
            affected.append(joint.child)
            self._find_child_links(joint.child, affected)
        return affected

    def _find_child_links(self, parent_link: str, affected: list):
        """递归查找子链接"""
        child_joints = [j for j in self.robot.joints if j.parent == parent_link]
        for child_joint in child_joints:
            affected.append(child_joint.child)
            self._find_child_links(child_joint.child, affected)

    def calculate_all_transforms(self):
        """计算所有链接的变换矩阵"""
        self.link_transforms = {}
        processed = set()

        def calculate_link_transform(link_name):
            if link_name in processed:
                return self.link_transforms.get(link_name, np.eye(4))

            joint = next((j for j in self.robot.joints if j.child == link_name), None)
            if not joint:
                transform = np.eye(4)
                self.link_transforms[link_name] = transform
                processed.add(link_name)
                return transform

            parent_transform = calculate_link_transform(joint.parent)
            joint_transform = self.calculate_joint_transform(joint)
            link_transform = np.dot(parent_transform, joint_transform)
            self.link_transforms[link_name] = link_transform
            processed.add(link_name)
            return link_transform

        for link in self.robot.links:
            calculate_link_transform(link.name)

    def calculate_joint_transform(self, joint):
        """计算单个关节的变换矩阵"""
        # 关节原点变换
        if joint.origin is not None:
            xyz = np.array(joint.origin.xyz)
            rpy = np.array(joint.origin.rpy)

            roll, pitch, yaw = rpy
            c_r, s_r = np.cos(roll), np.sin(roll)
            c_p, s_p = np.cos(pitch), np.sin(pitch)
            c_y, s_y = np.cos(yaw), np.sin(yaw)

            Rx = np.array([[1, 0, 0], [0, c_r, -s_r], [0, s_r, c_r]])
            Ry = np.array([[c_p, 0, s_p], [0, 1, 0], [-s_p, 0, c_p]])
            Rz = np.array([[c_y, -s_y, 0], [s_y, c_y, 0], [0, 0, 1]])

            origin_transform = np.eye(4)
            origin_transform[:3, :3] = np.dot(np.dot(Rz, Ry), Rx)
            origin_transform[:3, 3] = xyz
        else:
            origin_transform = np.eye(4)

        # 关节运动变换
        joint_value = self.joint_values.get(joint.name, 0.0)

        if joint.type == 'revolute':
            axis = np.array(joint.axis)
            if np.linalg.norm(axis) > 0:
                axis = axis / np.linalg.norm(axis)

                c = np.cos(joint_value)
                s = np.sin(joint_value)
                t = 1 - c
                x, y, z = axis

                joint_rotation = np.array([
                    [t*x*x + c,    t*x*y - s*z,  t*x*z + s*y],
                    [t*x*y + s*z,  t*y*y + c,    t*y*z - s*x],
                    [t*x*z - s*y,  t*y*z + s*x,  t*z*z + c]
                ])

                motion_transform = np.eye(4)
                motion_transform[:3, :3] = joint_rotation
            else:
                motion_transform = np.eye(4)

        elif joint.type == 'prismatic':
            axis = np.array(joint.axis)
            if np.linalg.norm(axis) > 0:
                axis = axis / np.linalg.norm(axis)
                translation = axis * joint_value

                motion_transform = np.eye(4)
                motion_transform[:3, 3] = translation
            else:
                motion_transform = np.eye(4)
        else:
            motion_transform = np.eye(4)

        return np.dot(origin_transform, motion_transform)

    def get_link_transform(self, link_name: str) -> np.ndarray:
        """获取指定链接的变换矩阵"""
        return self.link_transforms.get(link_name, np.eye(4))


class VTKURDFViewer(QFrame):
    loading_finished = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)

        # URDF相关
        self.robot = None
        self.urdf_dir = None
        self.mesh_actors = {}
        self.joint_values = {}
        self.axes_actor = None

        # 关节控制相关
        self.joint_analyzer = None
        self.finger_group_analyzer = None
        self.transform_calculator = None
        self.finger_group_sliders = {}
        self.individual_joint_sliders = {}

        # 材质和颜色
        self.link_color = QColor(180, 180, 180, 255)
        self.joint_color = QColor(255, 0, 0, 255)

        # VTK相关
        self.vtk_widget = None
        self.renderer = None
        self.render_window = None
        self.interactor = None

        self._setup_ui()
        self._setup_vtk()

    def _setup_ui(self):
        """设置UI布局"""
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)

        # 使用分割器创建左右布局
        splitter = QSplitter(Qt.Horizontal)

        # 左侧控制面板
        control_panel = self._create_control_panel()
        splitter.addWidget(control_panel)

        # 右侧VTK渲染窗口
        vtk_frame = QFrame()
        vtk_layout = QVBoxLayout(vtk_frame)
        vtk_layout.setContentsMargins(0, 0, 0, 0)

        self.vtk_widget = QVTKRenderWindowInteractor(vtk_frame)
        vtk_layout.addWidget(self.vtk_widget)

        # 底部控制按钮
        button_frame = QFrame()
        button_layout = QHBoxLayout(button_frame)

        self.load_button = QPushButton("加载URDF")
        self.load_button.clicked.connect(self.load_default_urdf)
        button_layout.addWidget(self.load_button)

        self.reset_button = QPushButton("重置视角")
        self.reset_button.clicked.connect(self.reset_camera)
        button_layout.addWidget(self.reset_button)

        self.info_button = QPushButton("显示信息")
        self.info_button.clicked.connect(self.show_scene_info)
        button_layout.addWidget(self.info_button)

        self.axes_button = QPushButton("切换坐标轴")
        self.axes_button.clicked.connect(self.toggle_axes)
        button_layout.addWidget(self.axes_button)

        vtk_layout.addWidget(button_frame)
        splitter.addWidget(vtk_frame)

        # 设置分割比例
        splitter.setSizes([400, 800])

        layout.addWidget(splitter)
        self.setLayout(layout)

    def _create_control_panel(self):
        """创建左侧控制面板"""
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumWidth(350)
        scroll_area.setMaximumWidth(450)

        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)

        # 材质控制组
        material_group = self._create_material_control_group()
        control_layout.addWidget(material_group)

        # 颜色控制组
        color_group = self._create_color_control_group()
        control_layout.addWidget(color_group)

        # 关节控制组（动态创建）
        self.joint_control_widget = QWidget()
        self.joint_control_layout = QVBoxLayout(self.joint_control_widget)
        control_layout.addWidget(self.joint_control_widget)

        # 添加弹性空间
        control_layout.addStretch()

        scroll_area.setWidget(control_widget)
        return scroll_area

    def _create_material_control_group(self):
        """创建材质控制组"""
        material_group = QGroupBox("材质控制")
        material_layout = QVBoxLayout(material_group)

        # 材质类型选择
        material_type_layout = QHBoxLayout()
        material_type_layout.addWidget(QLabel("材质类型:"))
        self.material_type_combo = QComboBox()
        self.material_type_combo.addItems([
            "Default", "Metallic", "Plastic", "Glass", "Robot"
        ])
        self.material_type_combo.setCurrentText("Robot")  # 默认选择Robot材质
        self.material_type_combo.currentTextChanged.connect(self.update_material_type)
        material_type_layout.addWidget(self.material_type_combo)
        material_layout.addLayout(material_type_layout)

        # 透明度控制
        transparency_layout = QHBoxLayout()
        transparency_layout.addWidget(QLabel("透明度:"))
        self.transparency_slider = QSlider(Qt.Horizontal)
        self.transparency_slider.setMinimum(0)
        self.transparency_slider.setMaximum(100)
        self.transparency_slider.setValue(0)
        self.transparency_slider.valueChanged.connect(self.update_transparency)
        self.transparency_label = QLabel("0%")
        transparency_layout.addWidget(self.transparency_slider)
        transparency_layout.addWidget(self.transparency_label)
        material_layout.addLayout(transparency_layout)

        # 连接滑块标签更新
        self.transparency_slider.valueChanged.connect(
            lambda v: self.transparency_label.setText(f"{v}%"))

        return material_group

    def _create_color_control_group(self):
        """创建颜色控制组"""
        color_group = QGroupBox("颜色控制")
        color_layout = QVBoxLayout(color_group)

        # 链接颜色
        link_color_layout = QHBoxLayout()
        link_color_layout.addWidget(QLabel("链接颜色:"))
        self.link_color_button = QPushButton("选择")
        self.link_color_button.clicked.connect(self.choose_link_color)
        link_color_layout.addWidget(self.link_color_button)
        color_layout.addLayout(link_color_layout)

        return color_group

    def update_material_type(self, material_type):
        """更新材质类型"""
        print(f"材质类型更改为: {material_type}")

        for actor_info in self.mesh_actors.values():
            property = actor_info['actor'].GetProperty()

            if material_type == "Robot":
                # 机器人材质：金属感，带反射
                property.SetColor(0.7, 0.7, 0.8)  # 银灰色
                property.SetAmbient(0.1)
                property.SetDiffuse(0.7)
                property.SetSpecular(0.8)
                property.SetSpecularPower(50)
                property.SetMetallic(0.8)
                property.SetRoughness(0.2)

            elif material_type == "Metallic":
                # 金属材质
                property.SetColor(0.8, 0.8, 0.9)  # 亮银色
                property.SetAmbient(0.05)
                property.SetDiffuse(0.6)
                property.SetSpecular(0.9)
                property.SetSpecularPower(80)
                property.SetMetallic(1.0)
                property.SetRoughness(0.1)

            elif material_type == "Plastic":
                # 塑料材质
                property.SetColor(0.9, 0.9, 0.9)  # 白色
                property.SetAmbient(0.2)
                property.SetDiffuse(0.8)
                property.SetSpecular(0.3)
                property.SetSpecularPower(20)
                property.SetMetallic(0.0)
                property.SetRoughness(0.6)

            elif material_type == "Glass":
                # 玻璃材质
                property.SetColor(0.8, 0.9, 1.0)  # 淡蓝色
                property.SetAmbient(0.1)
                property.SetDiffuse(0.3)
                property.SetSpecular(0.9)
                property.SetSpecularPower(100)
                property.SetOpacity(0.7)
                property.SetMetallic(0.0)
                property.SetRoughness(0.05)

            else:  # Default
                # 默认材质
                property.SetColor(0.8, 0.8, 0.8)  # 灰色
                property.SetAmbient(0.2)
                property.SetDiffuse(0.7)
                property.SetSpecular(0.3)
                property.SetSpecularPower(30)
                property.SetMetallic(0.0)
                property.SetRoughness(0.5)

        self.render_window.Render()

    def update_transparency(self, value):
        """更新透明度"""
        opacity = 1.0 - (value / 100.0)
        for actor_info in self.mesh_actors.values():
            actor_info['actor'].GetProperty().SetOpacity(opacity)
        self.render_window.Render()

    def choose_link_color(self):
        """选择链接颜色"""
        color = QColorDialog.getColor(self.link_color, self, "选择链接颜色")
        if color.isValid():
            self.link_color = color
            # 更新所有链接颜色
            r, g, b = color.red()/255.0, color.green()/255.0, color.blue()/255.0
            for actor_info in self.mesh_actors.values():
                actor_info['actor'].GetProperty().SetColor(r, g, b)
            self.render_window.Render()

    def _setup_vtk(self):
        """设置VTK渲染环境"""
        # 获取渲染窗口
        self.render_window = self.vtk_widget.GetRenderWindow()

        # 创建渲染器
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.2, 0.2, 0.3)  # 深蓝色背景，更容易看到白色模型
        self.render_window.AddRenderer(self.renderer)

        # 获取交互器
        self.interactor = self.vtk_widget.GetRenderWindow().GetInteractor()

        # 设置交互样式
        style = vtk.vtkInteractorStyleTrackballCamera()
        self.interactor.SetInteractorStyle(style)

        # 添加坐标轴
        self._add_coordinate_axes()

        # 添加光源
        self._setup_lighting()

        # 初始化交互器
        self.interactor.Initialize()

    def _add_coordinate_axes(self):
        """添加坐标轴"""
        axes = vtk.vtkAxesActor()
        # 设置更小的坐标轴，适合手部模型的尺寸
        axes.SetTotalLength(0.05, 0.05, 0.05)  # 5cm长度
        axes.SetShaftType(0)
        axes.SetAxisLabels(1)
        axes.SetCylinderRadius(0.002)  # 更细的轴

        self.axes_actor = axes  # 保存引用以便后续调整
        self.renderer.AddActor(axes)

    def _adjust_axes_size(self):
        """根据模型大小调整坐标轴"""
        if not self.axes_actor or not self.mesh_actors:
            return

        # 计算所有模型的边界
        all_bounds = [float('inf'), float('-inf'),
                     float('inf'), float('-inf'),
                     float('inf'), float('-inf')]

        for actor_info in self.mesh_actors.values():
            bounds = actor_info['actor'].GetBounds()
            all_bounds[0] = min(all_bounds[0], bounds[0])  # xmin
            all_bounds[1] = max(all_bounds[1], bounds[1])  # xmax
            all_bounds[2] = min(all_bounds[2], bounds[2])  # ymin
            all_bounds[3] = max(all_bounds[3], bounds[3])  # ymax
            all_bounds[4] = min(all_bounds[4], bounds[4])  # zmin
            all_bounds[5] = max(all_bounds[5], bounds[5])  # zmax

        # 计算模型的最大尺寸
        model_size = max(all_bounds[1] - all_bounds[0],
                        all_bounds[3] - all_bounds[2],
                        all_bounds[5] - all_bounds[4])

        # 坐标轴长度设为模型尺寸的1/3
        axes_length = model_size / 3
        self.axes_actor.SetTotalLength(axes_length, axes_length, axes_length)
        self.axes_actor.SetCylinderRadius(axes_length / 50)  # 轴的粗细

        print(f"模型尺寸: {model_size:.4f}m, 坐标轴长度: {axes_length:.4f}m")

    def _setup_lighting(self):
        """设置光照"""
        # 主光源
        light1 = vtk.vtkLight()
        light1.SetPosition(10, 10, 10)
        light1.SetFocalPoint(0, 0, 0)
        light1.SetColor(1.0, 1.0, 1.0)
        light1.SetIntensity(0.8)
        self.renderer.AddLight(light1)

        # 补光
        light2 = vtk.vtkLight()
        light2.SetPosition(-10, -10, 10)
        light2.SetFocalPoint(0, 0, 0)
        light2.SetColor(0.8, 0.8, 1.0)
        light2.SetIntensity(0.4)
        self.renderer.AddLight(light2)

    def load_urdf(self, urdf_path):
        """加载URDF文件"""
        try:
            print(f"加载URDF文件: {urdf_path}")

            # 清除现有模型
            self.clear_scene()

            # 解析URDF
            self.robot = URDF.from_xml_file(urdf_path)
            self.urdf_dir = os.path.dirname(os.path.abspath(urdf_path))

            # 加载所有链接的网格
            self._load_link_meshes()

            # 初始化关节值
            self._initialize_joints()

            # 调整坐标轴大小
            self._adjust_axes_size()

            # 分析关节信息
            self.joint_analyzer = URDFJointAnalyzer(urdf_path)
            self.joint_analyzer.print_joint_info()

            # 创建手指分组分析器
            if self.joint_analyzer.get_controllable_joints():
                self.finger_group_analyzer = FingerGroupAnalyzer(self.joint_analyzer)

                # 初始化变换计算器
                self.transform_calculator = SimpleTransformCalculator(self.robot)

                # 应用初始关节值到变换计算器
                self._apply_initial_joint_values()

                # 创建关节控制界面
                self.create_joint_controls()

                # 更新网格变换以显示初始姿态
                self.update_mesh_transforms()

            # 重置相机视角
            self.reset_camera()

            print(f"URDF加载完成，共{len(self.robot.links)}个链接")
            self.loading_finished.emit()

        except Exception as e:
            print(f"加载URDF失败: {e}")
            import traceback
            traceback.print_exc()

    def _load_link_meshes(self):
        """加载所有链接的网格"""
        for link in self.robot.links:
            print(1, link)
            if link.visuals:
                print(2)
                for i, visual in enumerate(link.visuals):
                    print(3, i, visual)
                    if visual.geometry and hasattr(visual.geometry, 'filename'):
                        print(4)
                        mesh_path = self._resolve_mesh_path(visual.geometry.filename)
                        print(5, mesh_path)
                        if mesh_path and os.path.exists(mesh_path):
                            print(6)
                            actor = self._create_mesh_actor(mesh_path, visual)
                            print(7, actor)
                            if actor:
                                actor_name = f"{link.name}_{i}"
                                self.mesh_actors[actor_name] = {
                                    'actor': actor,
                                    'link': link,
                                    'visual': visual
                                }
                                self.renderer.AddActor(actor)

    def _resolve_mesh_path(self, filename):
        """解析网格文件路径"""
        if filename.startswith('package://'):
            # 处理ROS包路径
            relative_path = filename.replace('package://', '')
            parts = relative_path.split('/', 1)
            if len(parts) == 2:
                package_name, mesh_file = parts
                mesh_path = os.path.join(self.urdf_dir, '..', 'meshes', mesh_file)
                return mesh_path
        elif filename.startswith('file://'):
            return filename[7:]  # 移除file://前缀
        else:
            # 相对路径
            return os.path.join(self.urdf_dir, filename)
        return filename

    def _create_mesh_actor(self, mesh_path, visual):
        """创建网格Actor"""
        try:
            # 使用trimesh加载网格
            mesh = trimesh.load(mesh_path)

            # 创建VTK点和面
            points = vtk.vtkPoints()
            for vertex in mesh.vertices:
                points.InsertNextPoint(vertex)

            # 创建面
            polys = vtk.vtkCellArray()
            for face in mesh.faces:
                polys.InsertNextCell(3)
                for vertex_id in face:
                    polys.InsertCellPoint(vertex_id)

            # 创建PolyData
            polydata = vtk.vtkPolyData()
            polydata.SetPoints(points)
            polydata.SetPolys(polys)

            # 计算法向量
            normals = vtk.vtkPolyDataNormals()
            normals.SetInputData(polydata)
            normals.ComputePointNormalsOn()
            normals.ComputeCellNormalsOn()
            normals.Update()

            # 创建Mapper
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(normals.GetOutputPort())

            # 创建Actor
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)

            # 设置材质属性
            self._apply_material(actor, visual)

            # 应用变换
            if visual.origin:
                transform = vtk.vtkTransform()
                if visual.origin.xyz:
                    transform.Translate(visual.origin.xyz)
                if visual.origin.rpy:
                    # RPY转换为旋转矩阵
                    r, p, y = visual.origin.rpy
                    transform.RotateZ(np.degrees(y))
                    transform.RotateY(np.degrees(p))
                    transform.RotateX(np.degrees(r))
                actor.SetUserTransform(transform)

            return actor

        except Exception as e:
            print(f"创建网格Actor失败 {mesh_path}: {e}")
            return None

    def _apply_material(self, actor, visual):
        """应用材质属性 - 默认使用机器人材质"""
        property = actor.GetProperty()

        # 默认使用机器人材质：金属感，带反射
        property.SetColor(0.7, 0.7, 0.8)  # 银灰色
        property.SetAmbient(0.1)
        property.SetDiffuse(0.7)
        property.SetSpecular(0.8)
        property.SetSpecularPower(50)
        property.SetMetallic(0.8)
        property.SetRoughness(0.2)

        # 如果有材质定义，可以覆盖颜色但保持机器人材质特性
        if visual.material:
            if visual.material.color:
                rgba = visual.material.color.rgba
                if rgba and len(rgba) >= 3:
                    # 保持机器人材质的金属特性，只改变颜色
                    property.SetColor(rgba[0], rgba[1], rgba[2])
                    if len(rgba) >= 4:
                        property.SetOpacity(rgba[3])

    def _initialize_joints(self):
        """初始化关节值 - 设置合理的初始姿态避免重叠"""
        if self.robot:
            for joint in self.robot.joints:
                if joint.type in ['revolute', 'continuous', 'prismatic']:
                    # 根据关节名称设置不同的初始值
                    initial_value = self._get_initial_joint_value(joint.name, joint)
                    self.joint_values[joint.name] = initial_value

    def _get_initial_joint_value(self, joint_name: str, joint) -> float:
        """根据关节名称获取合理的初始值 - 手完全张开状态"""
        # 获取关节限制
        if hasattr(joint, 'limit') and joint.limit:
            lower = joint.limit.lower if joint.limit.lower else 0
            upper = joint.limit.upper if joint.limit.upper else 3.14
        else:
            lower, upper = 0, 3.14

        # 根据关节名称模式设置初始值
        joint_name_lower = joint_name.lower()

        print(f"设置关节 {joint_name}: 范围[{lower:.3f}, {upper:.3f}]")

        # 所有手指关节都设置为最大值，完全伸展
        if any(finger in joint_name_lower for finger in ['thumb', 'index', 'middle', 'ring', 'pinky']):
            print(f"  {joint_name} 设置为最大值(完全伸展): {upper:.3f}")
            return lower  # 设置为上限，完全伸展

        # 手腕或其他关节保持中性位置
        else:
            value = (upper + lower) * 0.5
            print(f"  中性位置: {value:.3f}")
            return value

    def _apply_initial_joint_values(self):
        """将初始关节值应用到变换计算器"""
        if self.transform_calculator:
            for joint_name, value in self.joint_values.items():
                self.transform_calculator.update_joint_value(joint_name, value)
            print(f"应用了 {len(self.joint_values)} 个关节的初始值")

    def clear_scene(self):
        """清除场景中的所有网格"""
        for actor_info in self.mesh_actors.values():
            self.renderer.RemoveActor(actor_info['actor'])
        self.mesh_actors.clear()
        self.render_window.Render()

    def reset_camera(self):
        """重置相机视角"""
        self.renderer.ResetCamera()

        # 获取场景边界
        bounds = self.renderer.ComputeVisiblePropBounds()
        if bounds[0] != bounds[1]:  # 确保有有效的边界
            # 计算场景中心
            center = [(bounds[0] + bounds[1]) / 2,
                     (bounds[2] + bounds[3]) / 2,
                     (bounds[4] + bounds[5]) / 2]

            # 计算场景大小
            size = max(bounds[1] - bounds[0],
                      bounds[3] - bounds[2],
                      bounds[5] - bounds[4])

            # 设置相机位置
            camera = self.renderer.GetActiveCamera()
            camera.SetFocalPoint(center[0], center[1], center[2])
            camera.SetPosition(center[0] + size * 2,
                             center[1] + size * 2,
                             center[2] + size * 1)
            camera.SetViewUp(0, 0, 1)

            print(f"场景边界: {bounds}")
            print(f"场景中心: {center}")
            print(f"场景大小: {size}")
        else:
            # 默认视角
            self.renderer.GetActiveCamera().SetPosition(0.2, 0.2, 0.2)
            self.renderer.GetActiveCamera().SetFocalPoint(0, 0, 0.05)
            self.renderer.GetActiveCamera().SetViewUp(0, 0, 1)

        self.render_window.Render()

    def load_default_urdf(self):
        """加载默认URDF文件"""
        urdf_path = "C:/Users/<USER>/Downloads/URDF_demo/brainco-righthand-URDF-V2/urdf/brainco-righthand-URDF-V2_converted.urdf"
        urdf_path = "C:/Users/<USER>/Downloads/URDF_demo/brainco-lefthand-URDF-V2/urdf/brainco-lefthand-URDF-V2_converted.urdf"
        if os.path.exists(urdf_path):
            self.load_urdf(urdf_path)
        else:
            print(f"默认URDF文件不存在: {urdf_path}")

    def show_scene_info(self):
        """显示场景信息"""
        print("\n=== 场景信息 ===")
        print(f"已加载的Actor数量: {len(self.mesh_actors)}")

        for name, info in self.mesh_actors.items():
            actor = info['actor']
            bounds = actor.GetBounds()
            print(f"Actor: {name}")
            print(f"  边界: X({bounds[0]:.4f}, {bounds[1]:.4f}) "
                  f"Y({bounds[2]:.4f}, {bounds[3]:.4f}) "
                  f"Z({bounds[4]:.4f}, {bounds[5]:.4f})")
            print(f"  可见性: {actor.GetVisibility()}")

        # 渲染器信息
        bounds = self.renderer.ComputeVisiblePropBounds()
        print(f"\n渲染器边界: {bounds}")

        # 相机信息
        camera = self.renderer.GetActiveCamera()
        pos = camera.GetPosition()
        focal = camera.GetFocalPoint()
        print(f"相机位置: {pos}")
        print(f"焦点: {focal}")
        print("================\n")

    def toggle_axes(self):
        """切换坐标轴显示"""
        if self.axes_actor:
            current_visibility = self.axes_actor.GetVisibility()
            self.axes_actor.SetVisibility(not current_visibility)
            self.render_window.Render()
            print(f"坐标轴显示: {'开' if not current_visibility else '关'}")

    def create_joint_controls(self):
        """创建关节控制界面"""
        if not self.finger_group_analyzer:
            return

        finger_groups = self.finger_group_analyzer.get_finger_groups()

        # 清除现有控件
        while self.joint_control_layout.count():
            item = self.joint_control_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # 创建手指联动控制组
        finger_groups_data = finger_groups['finger_groups']
        if finger_groups_data:
            finger_control_group = QGroupBox("手指联动控制")
            finger_control_layout = QVBoxLayout(finger_control_group)

            for group_key, group_config in finger_groups_data.items():
                group_widget = self.create_finger_group_control(group_key, group_config)
                finger_control_layout.addWidget(group_widget)

            self.joint_control_layout.addWidget(finger_control_group)

        # 创建独立关节控制组
        individual_joints = finger_groups['individual_joints']
        if individual_joints:
            individual_control_group = QGroupBox("独立关节控制")
            individual_control_layout = QVBoxLayout(individual_control_group)

            for joint_name, joint_config in individual_joints.items():
                joint_widget = self.create_individual_joint_control(joint_name, joint_config)
                individual_control_layout.addWidget(joint_widget)

            self.joint_control_layout.addWidget(individual_control_group)

        # 创建预设动作控制组
        preset_control_group = self.create_preset_control_group()
        self.joint_control_layout.addWidget(preset_control_group)

    def create_finger_group_control(self, group_key: str, group_config: Dict):
        """创建手指组控制"""
        group_frame = QFrame()
        group_layout = QVBoxLayout(group_frame)

        # 标题
        title_label = QLabel(f"🖐 {group_config['name']}")
        title_label.setStyleSheet("font-weight: bold; color: #2E86AB;")
        group_layout.addWidget(title_label)

        # 滑块控制
        slider_layout = QHBoxLayout()

        # 获取关节范围
        joints = group_config['joints']
        if joints:
            first_joint_info = group_config['joint_info'][joints[0]]
            min_val = first_joint_info['lower']
            max_val = first_joint_info['upper']

            slider = QSlider(Qt.Horizontal)
            slider.setMinimum(int(min_val * 100))
            slider.setMaximum(int(max_val * 100))

            # 设置初始值为第一个关节的当前值
            first_joint_name = joints[0]
            initial_value = self.joint_values.get(first_joint_name, 0.0)
            slider.setValue(int(initial_value * 100))

            slider.valueChanged.connect(
                lambda value, gk=group_key, gc=group_config:
                self.on_finger_group_changed(gk, gc, value / 100.0)
            )

            value_label = QLabel(f"{initial_value:.2f}")
            slider.valueChanged.connect(
                lambda value, label=value_label:
                label.setText(f"{value/100.0:.2f}")
            )

            self.finger_group_sliders[group_key] = slider

            slider_layout.addWidget(slider)
            slider_layout.addWidget(value_label)

        group_layout.addLayout(slider_layout)

        # 描述
        if group_config.get('description'):
            desc_label = QLabel(group_config['description'])
            desc_label.setStyleSheet("font-size: 10px; color: #666;")
            desc_label.setWordWrap(True)
            group_layout.addWidget(desc_label)

        return group_frame

    def create_individual_joint_control(self, joint_name: str, joint_config: Dict):
        """创建独立关节控制"""
        joint_frame = QFrame()
        joint_layout = QVBoxLayout(joint_frame)

        # 标题
        title_label = QLabel(f"⚙ {joint_name}")
        title_label.setStyleSheet("font-weight: bold; color: #A23B72;")
        joint_layout.addWidget(title_label)

        # 滑块控制
        slider_layout = QHBoxLayout()

        joint_info = joint_config['info']
        min_val = joint_info['lower']
        max_val = joint_info['upper']

        slider = QSlider(Qt.Horizontal)
        slider.setMinimum(int(min_val * 100))
        slider.setMaximum(int(max_val * 100))

        # 设置初始值为当前关节值
        initial_value = self.joint_values.get(joint_name, 0.0)
        slider.setValue(int(initial_value * 100))

        slider.valueChanged.connect(
            lambda value, jn=joint_name:
            self.on_individual_joint_changed(jn, value / 100.0)
        )

        value_label = QLabel(f"{initial_value:.2f}")
        slider.valueChanged.connect(
            lambda value, label=value_label:
            label.setText(f"{value/100.0:.2f}")
        )

        self.individual_joint_sliders[joint_name] = slider

        slider_layout.addWidget(slider)
        slider_layout.addWidget(value_label)

        joint_layout.addLayout(slider_layout)

        return joint_frame

    def create_preset_control_group(self):
        """创建预设动作控制组"""
        preset_group = QGroupBox("预设动作")
        preset_layout = QVBoxLayout(preset_group)

        # 第一行按钮
        row1_layout = QHBoxLayout()

        open_hand_btn = QPushButton("张开手")
        open_hand_btn.clicked.connect(self.preset_open_hand)
        row1_layout.addWidget(open_hand_btn)

        close_hand_btn = QPushButton("握拳")
        close_hand_btn.clicked.connect(self.preset_close_hand)
        row1_layout.addWidget(close_hand_btn)

        preset_layout.addLayout(row1_layout)

        # 第二行按钮
        row2_layout = QHBoxLayout()

        peace_btn = QPushButton("比V")
        peace_btn.clicked.connect(self.preset_peace_sign)
        row2_layout.addWidget(peace_btn)

        reset_btn = QPushButton("重置")
        reset_btn.clicked.connect(self.preset_reset)
        row2_layout.addWidget(reset_btn)

        preset_layout.addLayout(row2_layout)

        return preset_group

    def on_finger_group_changed(self, group_key: str, group_config: Dict, value: float):
        """手指组滑块变化回调"""
        joints = group_config['joints']
        ratios = group_config['ratios']

        for i, joint_name in enumerate(joints):
            joint_value = value * ratios[i]
            if self.transform_calculator:
                self.transform_calculator.update_joint_value(joint_name, joint_value)

        self.update_mesh_transforms()

    def on_individual_joint_changed(self, joint_name: str, value: float):
        """独立关节滑块变化回调"""
        if self.transform_calculator:
            self.transform_calculator.update_joint_value(joint_name, value)
        self.update_mesh_transforms()

    def update_mesh_transforms(self):
        """更新网格变换"""
        if not self.transform_calculator:
            return

        for actor_name, actor_info in self.mesh_actors.items():
            link_name = actor_info['link'].name
            transform_matrix = self.transform_calculator.get_link_transform(link_name)

            # 转换为VTK变换
            vtk_transform = vtk.vtkTransform()
            vtk_matrix = vtk.vtkMatrix4x4()

            for i in range(4):
                for j in range(4):
                    vtk_matrix.SetElement(i, j, transform_matrix[i, j])

            vtk_transform.SetMatrix(vtk_matrix)
            actor_info['actor'].SetUserTransform(vtk_transform)

        self.render_window.Render()

    def preset_open_hand(self):
        """预设动作：张开手"""
        self.set_all_finger_groups(0.0)

    def preset_close_hand(self):
        """预设动作：握拳"""
        # 设置所有手指弯曲
        for group_key in self.finger_group_sliders:
            if 'thumb_rotation' not in group_key:  # 除了拇指转动
                self.set_finger_group_value(group_key, 1.0)

    def preset_peace_sign(self):
        """预设动作：比V"""
        self.set_all_finger_groups(0.0)  # 先重置
        # 食指和中指伸直，其他弯曲
        self.set_finger_group_value('ring', 1.0)
        self.set_finger_group_value('pinky', 1.0)

    def preset_reset(self):
        """预设动作：重置"""
        self.set_all_finger_groups(0.0)

    def set_all_finger_groups(self, value: float):
        """设置所有手指组的值"""
        for group_key in self.finger_group_sliders:
            self.set_finger_group_value(group_key, value)

    def set_finger_group_value(self, group_key: str, value: float):
        """设置特定手指组的值"""
        if group_key in self.finger_group_sliders:
            slider = self.finger_group_sliders[group_key]
            slider_value = int(value * (slider.maximum() - slider.minimum()) + slider.minimum())
            slider.setValue(slider_value)

    def cleanup_resources(self):
        """清理VTK资源"""
        if self.interactor:
            self.interactor.TerminateApp()
        self.clear_scene()


class TestVTKWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VTK URDF查看器测试")
        self.setGeometry(100, 100, 1000, 700)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # 创建VTK URDF查看器
        self.vtk_viewer = VTKURDFViewer()
        layout.addWidget(self.vtk_viewer)

        # 连接信号
        self.vtk_viewer.loading_finished.connect(self.on_loading_finished)

    def on_loading_finished(self):
        print("URDF加载完成！")

    def closeEvent(self, event):
        """关闭时清理VTK资源"""
        if hasattr(self, 'vtk_viewer'):
            self.vtk_viewer.cleanup_resources()
        event.accept()


def main():
    app = QApplication(sys.argv)

    window = TestVTKWindow()
    window.show()

    print("VTK URDF查看器测试启动")
    print("点击'加载URDF'按钮加载模型")
    print("使用鼠标拖拽旋转视角，滚轮缩放")

    sys.exit(app.exec())


if __name__ == "__main__":
    main()