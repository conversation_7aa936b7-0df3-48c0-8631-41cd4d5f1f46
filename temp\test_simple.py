#!/usr/bin/env python3
"""
简化的URDF查看器测试 - 专门解决坐标轴大小问题
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QHBoxLayout, QPushButton
import vtk
from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
from urdf_parser_py.urdf import URDF
import trimesh
import numpy as np


class SimpleURDFViewer(QWidget):
    def __init__(self):
        super().__init__()
        self.robot = None
        self.mesh_actors = {}
        self.axes_actor = None
        
        self.setup_ui()
        self.setup_vtk()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # VTK窗口
        self.vtk_widget = QVTKRenderWindowInteractor(self)
        layout.addWidget(self.vtk_widget)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        load_btn = QPushButton("加载URDF")
        load_btn.clicked.connect(self.load_urdf)
        button_layout.addWidget(load_btn)
        
        reset_btn = QPushButton("重置视角")
        reset_btn.clicked.connect(self.reset_camera)
        button_layout.addWidget(reset_btn)
        
        axes_btn = QPushButton("切换坐标轴")
        axes_btn.clicked.connect(self.toggle_axes)
        button_layout.addWidget(axes_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
        
    def setup_vtk(self):
        # 渲染器
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.2, 0.2, 0.3)
        
        # 渲染窗口
        self.render_window = self.vtk_widget.GetRenderWindow()
        self.render_window.AddRenderer(self.renderer)
        
        # 交互器
        self.interactor = self.vtk_widget.GetRenderWindow().GetInteractor()
        style = vtk.vtkInteractorStyleTrackballCamera()
        self.interactor.SetInteractorStyle(style)
        
        # 添加小坐标轴
        self.add_axes()
        
        # 光照
        light = vtk.vtkLight()
        light.SetPosition(0.2, 0.2, 0.2)
        light.SetFocalPoint(0, 0, 0)
        self.renderer.AddLight(light)
        
        self.interactor.Initialize()
        
    def add_axes(self):
        """添加小坐标轴"""
        self.axes_actor = vtk.vtkAxesActor()
        self.axes_actor.SetTotalLength(0.02, 0.02, 0.02)  # 2cm
        self.axes_actor.SetCylinderRadius(0.001)
        self.renderer.AddActor(self.axes_actor)
        
    def load_urdf(self):
        urdf_path = "C:/Users/<USER>/Downloads/URDF_demo/brainco-righthand-URDF-V2/urdf/brainco-righthand-URDF-V2_converted.urdf"
        
        try:
            print("加载URDF...")
            self.robot = URDF.from_xml_file(urdf_path)
            urdf_dir = os.path.dirname(urdf_path)
            
            # 清除旧模型
            for actor_info in self.mesh_actors.values():
                self.renderer.RemoveActor(actor_info['actor'])
            self.mesh_actors.clear()
            
            # 加载网格
            loaded_count = 0
            for link in self.robot.links:
                if link.visuals:
                    for i, visual in enumerate(link.visuals):
                        if visual.geometry and hasattr(visual.geometry, 'filename'):
                            mesh_path = visual.geometry.filename
                            if os.path.exists(mesh_path):
                                actor = self.create_mesh_actor(mesh_path, visual)
                                if actor:
                                    actor_name = f"{link.name}_{i}"
                                    self.mesh_actors[actor_name] = {'actor': actor}
                                    self.renderer.AddActor(actor)
                                    loaded_count += 1
                                    
            print(f"加载了 {loaded_count} 个网格")
            
            # 调整坐标轴大小
            self.adjust_axes_size()
            
            # 重置视角
            self.reset_camera()
            
        except Exception as e:
            print(f"加载失败: {e}")
            import traceback
            traceback.print_exc()
            
    def create_mesh_actor(self, mesh_path, visual):
        """创建网格Actor"""
        try:
            mesh = trimesh.load(mesh_path)
            
            # VTK点
            points = vtk.vtkPoints()
            for vertex in mesh.vertices:
                points.InsertNextPoint(vertex)
                
            # VTK面
            polys = vtk.vtkCellArray()
            for face in mesh.faces:
                polys.InsertNextCell(3)
                for vertex_id in face:
                    polys.InsertCellPoint(vertex_id)
                    
            # PolyData
            polydata = vtk.vtkPolyData()
            polydata.SetPoints(points)
            polydata.SetPolys(polys)
            
            # 法向量
            normals = vtk.vtkPolyDataNormals()
            normals.SetInputData(polydata)
            normals.Update()
            
            # Mapper
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(normals.GetOutputPort())
            
            # Actor
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            
            # 材质 - 使用明显的颜色
            prop = actor.GetProperty()
            prop.SetColor(0.8, 0.6, 0.4)  # 浅棕色
            prop.SetAmbient(0.3)
            prop.SetDiffuse(0.8)
            prop.SetSpecular(0.3)
            
            return actor
            
        except Exception as e:
            print(f"创建Actor失败: {e}")
            return None
            
    def adjust_axes_size(self):
        """根据模型大小调整坐标轴"""
        if not self.mesh_actors:
            return
            
        # 计算模型边界
        bounds = [float('inf'), float('-inf')] * 3
        for actor_info in self.mesh_actors.values():
            actor_bounds = actor_info['actor'].GetBounds()
            bounds[0] = min(bounds[0], actor_bounds[0])
            bounds[1] = max(bounds[1], actor_bounds[1])
            bounds[2] = min(bounds[2], actor_bounds[2])
            bounds[3] = max(bounds[3], actor_bounds[3])
            bounds[4] = min(bounds[4], actor_bounds[4])
            bounds[5] = max(bounds[5], actor_bounds[5])
            
        # 模型最大尺寸
        size = max(bounds[1]-bounds[0], bounds[3]-bounds[2], bounds[5]-bounds[4])
        
        # 坐标轴为模型尺寸的1/4
        axes_size = size / 4
        self.axes_actor.SetTotalLength(axes_size, axes_size, axes_size)
        self.axes_actor.SetCylinderRadius(axes_size / 50)
        
        print(f"模型尺寸: {size:.4f}m, 坐标轴: {axes_size:.4f}m")
        
    def reset_camera(self):
        """重置相机"""
        self.renderer.ResetCamera()
        
        # 获取场景边界
        bounds = self.renderer.ComputeVisiblePropBounds()
        if bounds[0] != bounds[1]:
            center = [(bounds[0]+bounds[1])/2, (bounds[2]+bounds[3])/2, (bounds[4]+bounds[5])/2]
            size = max(bounds[1]-bounds[0], bounds[3]-bounds[2], bounds[5]-bounds[4])
            
            camera = self.renderer.GetActiveCamera()
            camera.SetFocalPoint(center[0], center[1], center[2])
            camera.SetPosition(center[0] + size*1.5, center[1] + size*1.5, center[2] + size*1)
            camera.SetViewUp(0, 0, 1)
            
        self.render_window.Render()
        
    def toggle_axes(self):
        """切换坐标轴"""
        if self.axes_actor:
            visible = self.axes_actor.GetVisibility()
            self.axes_actor.SetVisibility(not visible)
            self.render_window.Render()
            print(f"坐标轴: {'隐藏' if visible else '显示'}")


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("URDF查看器 - 坐标轴修复版")
        self.setGeometry(100, 100, 1000, 700)
        
        self.viewer = SimpleURDFViewer()
        self.setCentralWidget(self.viewer)


def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    
    print("URDF查看器启动")
    print("点击'加载URDF'加载模型")
    print("点击'切换坐标轴'来隐藏/显示坐标轴")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
